import type { NextApiRequest, NextApiResponse } from 'next';

interface DiagnosticResult {
  service: string;
  status: 'healthy' | 'degraded' | 'error' | 'unreachable';
  details: any;
  timestamp: string;
}

interface DiagnosticResponse {
  overall_status: 'healthy' | 'degraded' | 'error';
  services: DiagnosticResult[];
  recommendations: string[];
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DiagnosticResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      overall_status: 'error',
      services: [],
      recommendations: ['Use POST method'],
      timestamp: new Date().toISOString()
    });
  }

  const services: DiagnosticResult[] = [];
  const recommendations: string[] = [];

  // Determine environment and URLs
  const isProduction = process.env.NODE_ENV === 'production' || 
                      (req.headers.host && req.headers.host.includes('uruenterprises.com'));
  
  const oauthUrl = process.env.NEXT_PUBLIC_OAUTH_URL || 
                   (isProduction ? 'https://oauth.uruenterprises.com' : 'http://localhost:8000');
  
  const mcpUrl = process.env.NEXT_PUBLIC_MCP_URL || 
                 (isProduction ? 'https://mcp.uruenterprises.com' : 'http://localhost:3001');

  // Test OAuth Service Health
  try {
    console.log('🔍 Testing OAuth service health at:', oauthUrl);
    const oauthResponse = await fetch(`${oauthUrl}/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    const oauthData = await oauthResponse.json();
    
    services.push({
      service: 'OAuth Service',
      status: oauthData.status === 'healthy' ? 'healthy' : 
              oauthData.status === 'degraded' ? 'degraded' : 'error',
      details: {
        url: oauthUrl,
        response_status: oauthResponse.status,
        database: oauthData.database || 'unknown',
        error: oauthData.error || null,
        supabase_version: oauthData.supabase_version || null,
        fastapi_version: oauthData.fastapi_version || null
      },
      timestamp: new Date().toISOString()
    });

    // Add specific recommendations based on OAuth service status
    if (oauthData.status === 'degraded' || oauthData.database === 'disconnected') {
      recommendations.push('OAuth service database connection is down - check Supabase credentials');
      recommendations.push('Verify SUPABASE_URL and SUPABASE_KEY environment variables');
      recommendations.push('Check if Supabase service is accessible from Elestio deployment');
    }

  } catch (error) {
    console.error('❌ OAuth service health check failed:', error);
    
    services.push({
      service: 'OAuth Service',
      status: 'unreachable',
      details: {
        url: oauthUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
        error_type: error instanceof Error ? error.constructor.name : 'Unknown'
      },
      timestamp: new Date().toISOString()
    });

    recommendations.push('OAuth service is unreachable - check if service is running');
    recommendations.push('Verify OAuth service URL configuration');
    recommendations.push('Check Docker container status and network connectivity');
  }

  // Test MCP Proxy Health
  try {
    console.log('🔍 Testing MCP proxy health at:', mcpUrl);
    const mcpResponse = await fetch(`${mcpUrl}/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    const mcpData = await mcpResponse.json();
    
    services.push({
      service: 'MCP Proxy',
      status: mcpData.status === 'Smart MCP Proxy running' ? 'healthy' : 'error',
      details: {
        url: mcpUrl,
        response_status: mcpResponse.status,
        version: mcpData.version || null,
        features: mcpData.features || [],
        oauth_service: mcpData.oauth_service || null,
        n8n_connection: mcpData.n8n_connection || null
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ MCP proxy health check failed:', error);
    
    services.push({
      service: 'MCP Proxy',
      status: 'unreachable',
      details: {
        url: mcpUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
        error_type: error instanceof Error ? error.constructor.name : 'Unknown'
      },
      timestamp: new Date().toISOString()
    });

    recommendations.push('MCP proxy is unreachable - check if service is running');
  }

  // Test Login Endpoint Specifically
  try {
    console.log('🔍 Testing login endpoint specifically...');
    const loginResponse = await fetch(`${oauthUrl}/auth/login`, {
      method: 'POST',
      body: new FormData(), // Empty form data to trigger validation
      signal: AbortSignal.timeout(5000),
    });

    // We expect this to fail with validation error, not 503
    const loginText = await loginResponse.text();
    let loginData;
    try {
      loginData = JSON.parse(loginText);
    } catch {
      loginData = { raw_response: loginText };
    }

    services.push({
      service: 'Login Endpoint',
      status: loginResponse.status === 503 ? 'error' : 
              loginResponse.status === 422 ? 'healthy' : 'degraded',
      details: {
        url: `${oauthUrl}/auth/login`,
        response_status: loginResponse.status,
        response_data: loginData,
        expected_422_for_empty_form: loginResponse.status === 422
      },
      timestamp: new Date().toISOString()
    });

    if (loginResponse.status === 503) {
      recommendations.push('Login endpoint returning 503 - authentication service disabled due to database issues');
    }

  } catch (error) {
    services.push({
      service: 'Login Endpoint',
      status: 'unreachable',
      details: {
        url: `${oauthUrl}/auth/login`,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      timestamp: new Date().toISOString()
    });
  }

  // Determine overall status
  const hasUnreachable = services.some(s => s.status === 'unreachable');
  const hasError = services.some(s => s.status === 'error');
  const hasDegraded = services.some(s => s.status === 'degraded');

  let overall_status: 'healthy' | 'degraded' | 'error';
  if (hasUnreachable || hasError) {
    overall_status = 'error';
  } else if (hasDegraded) {
    overall_status = 'degraded';
  } else {
    overall_status = 'healthy';
  }

  // Add environment-specific recommendations
  if (isProduction) {
    recommendations.push('Running in production mode - check Elestio deployment logs');
    recommendations.push('Verify all environment variables are set in Elestio dashboard');
  } else {
    recommendations.push('Running in development mode - check local Docker containers');
  }

  res.status(200).json({
    overall_status,
    services,
    recommendations,
    timestamp: new Date().toISOString()
  });
}
