import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { ArrowRight, Building2, Zap, MessageSquare, Search, Lock, Brain, ExternalLink, CheckCircle, Circle, Shield, Users } from 'lucide-react';
import Link from 'next/link';
import { apiService } from '../utils/api';

// Enhanced fallback icon component with better styling
const FallbackIcon = ({ name, className }: { name: string; className?: string }) => {
  const initials = name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);

  return (
    <div className={`bg-gradient-to-br from-uru-blue-600 to-uru-cyan-600 rounded-lg flex items-center justify-center shadow-lg border border-uru-blue-500/30 ${className || 'w-full h-full'}`}>
      <span className="text-white font-bold text-sm sm:text-base md:text-lg">{initials}</span>
    </div>
  );
};

// High-quality logo component with multiple fallback sources and loading state
const ServiceIcon = ({ integration }: { integration: any }) => {
  const [currentSource, setCurrentSource] = useState(0);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    console.warn(`Failed to load icon source ${currentSource} for ${integration.name}`);
    if (currentSource < integration.sources.length - 1) {
      setCurrentSource(currentSource + 1);
      setIsLoading(true);
    } else {
      setHasError(true);
      setIsLoading(false);
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  if (hasError) {
    return <FallbackIcon name={integration.name} className="w-full h-full" />;
  }

  return (
    <div className="w-full h-full relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800/50 rounded-lg">
          <div className="w-4 h-4 border-2 border-uru-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <img
        src={integration.sources[currentSource]}
        alt={`${integration.name} logo`}
        className={`w-full h-full object-contain transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
      />
    </div>
  );
};

export default function HomePage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [oauthStatus, setOauthStatus] = useState<any>(null);
  const [serviceConnections, setServiceConnections] = useState<{[key: string]: boolean}>({});
  const [showDemoModal, setShowDemoModal] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);

  // Handler functions for CTAs
  const handleWatchDemo = () => {
    setShowDemoModal(true);
  };

  const handleScheduleDemo = () => {
    // Open Calendly or similar scheduling tool
    window.open('https://calendly.com/uru-discovery', '_blank');
  };

  const handleContactForm = () => {
    setShowContactModal(true);
  };

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  // Check authentication status and OAuth connections on mount
  useEffect(() => {
    const checkAuthAndConnections = async () => {
      try {
        if (apiService.isAuthenticated()) {
          setIsAuthenticated(true);

          // Get OAuth status for authenticated users
          const status = await apiService.getOAuthStatus();
          setOauthStatus(status);

          // Set individual service connection status
          if (status.connected && status.scopes) {
            const connections: {[key: string]: boolean} = {};

            // Check which services are connected based on scopes
            connections.gmail = status.scopes.some((scope: string) => scope.includes('gmail'));
            connections.gdrive = status.scopes.some((scope: string) => scope.includes('drive'));
            connections.calendar = status.scopes.some((scope: string) => scope.includes('calendar'));

            setServiceConnections(connections);
          }
        }
      } catch (error) {
        console.log('Not authenticated or error checking OAuth status:', error);
        setIsAuthenticated(false);
      }
    };

    checkAuthAndConnections();
  }, []);

  // Get connection status for a service
  const getServiceConnectionStatus = (serviceId: string) => {
    if (!isAuthenticated) return 'available'; // Show as available for non-authenticated users

    switch (serviceId) {
      case 'gmail':
      case 'gdrive':
      case 'calendar':
        return serviceConnections[serviceId] ? 'connected' : 'available';
      default:
        return 'available'; // Other services are shown as available
    }
  };

  const integrations = [
    {
      id: 'gmail',
      name: 'Gmail',
      category: 'google',
      description: 'Email management and communication',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/7/7e/Gmail_icon_%282020%29.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/gmail.svg',
        'https://img.icons8.com/color/96/gmail-new.png'
      ]
    },
    {
      id: 'gdrive',
      name: 'Google Drive',
      category: 'google',
      description: 'File storage and document management',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/1/12/Google_Drive_icon_%282020%29.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/google-drive.svg',
        'https://img.icons8.com/color/96/google-drive--v1.png'
      ]
    },
    {
      id: 'calendar',
      name: 'Google Calendar',
      category: 'google',
      description: 'Calendar management and scheduling',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/google-calendar.svg',
        'https://img.icons8.com/color/96/google-calendar--v1.png'
      ]
    },
    {
      id: 'slack',
      name: 'Slack',
      category: 'communication',
      description: 'Team communication and collaboration',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/slack.svg',
        'https://img.icons8.com/color/96/slack-new.png'
      ]
    },
    {
      id: 'notion',
      name: 'Notion',
      category: 'productivity',
      description: 'Knowledge management and documentation',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/notebook.svg',
        'https://img.icons8.com/color/96/notion--v1.png'
      ]
    },
    {
      id: 'github',
      name: 'GitHub',
      category: 'development',
      description: 'Code repositories and version control',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/9/91/Octicons-mark-github.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/github.svg',
        'https://img.icons8.com/color/96/github--v1.png'
      ]
    },
    {
      id: 'microsoft',
      name: 'Microsoft 365',
      category: 'productivity',
      description: 'Office suite and productivity tools',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/0/0e/Microsoft_365_%282022%29.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/microsoft-office.svg',
        'https://img.icons8.com/color/96/microsoft-office-2019.png'
      ]
    },
    {
      id: 'dropbox',
      name: 'Dropbox',
      category: 'storage',
      description: 'Cloud file storage and sharing',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/c/cb/Dropbox_logo_2017.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/dropbox.svg',
        'https://img.icons8.com/color/96/dropbox.png'
      ]
    },
    {
      id: 'teams',
      name: 'Microsoft Teams',
      category: 'communication',
      description: 'Team collaboration and video conferencing',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/c/c9/Microsoft_Office_Teams_%282018%E2%80%93present%29.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/microsoft-teams.svg',
        'https://img.icons8.com/color/96/microsoft-teams.png'
      ]
    },
    {
      id: 'asana',
      name: 'Asana',
      category: 'productivity',
      description: 'Project management and team coordination',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/3/3b/Asana_logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/chart-gantt.svg',
        'https://img.icons8.com/color/96/asana.png'
      ]
    },
    {
      id: 'salesforce',
      name: 'Salesforce',
      category: 'crm',
      description: 'Customer relationship management',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/salesforce.svg',
        'https://img.icons8.com/color/96/salesforce.png'
      ]
    },
    {
      id: 'hubspot',
      name: 'HubSpot',
      category: 'crm',
      description: 'Inbound marketing and sales platform',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/3/3f/HubSpot_Logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/hubspot.svg',
        'https://img.icons8.com/color/96/hubspot.png'
      ]
    },
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      category: 'financial',
      description: 'Accounting and financial management',
      sources: [
        'https://img.icons8.com/color/96/quickbooks.png',
        'https://cdn.worldvectorlogo.com/logos/quickbooks-1.svg',
        'https://upload.wikimedia.org/wikipedia/commons/c/ce/Intuit_QuickBooks_logo_2022.svg'
      ]
    },
    {
      id: 'zoom',
      name: 'Zoom',
      category: 'communication',
      description: 'Video conferencing and webinars',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/7/7b/Zoom_Communications_Logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/video.svg',
        'https://img.icons8.com/color/96/zoom.png'
      ]
    },
    {
      id: 'jira',
      name: 'Jira',
      category: 'development',
      description: 'Issue tracking and project management',
      sources: [
        'https://logos-world.net/wp-content/uploads/2021/02/Jira-Logo.png',
        'https://upload.wikimedia.org/wikipedia/commons/8/82/Jira_%28Software%29_logo.svg',
        'https://img.icons8.com/color/96/jira.png'
      ]
    },
    {
      id: 'trello',
      name: 'Trello',
      category: 'productivity',
      description: 'Visual project management and collaboration',
      sources: [
        'https://upload.wikimedia.org/wikipedia/en/8/8c/Trello_logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/trello.svg',
        'https://img.icons8.com/color/96/trello.png'
      ]
    },
    {
      id: 'figma',
      name: 'Figma',
      category: 'design',
      description: 'Design and prototyping platform',
      sources: [
        'https://upload.wikimedia.org/wikipedia/commons/3/33/Figma-logo.svg',
        'https://cdn.jsdelivr.net/npm/@mdi/svg@7.4.47/svg/figma.svg',
        'https://img.icons8.com/color/96/figma--v1.png'
      ]
    }
  ];

  const features = [
    {
      id: 'navigation',
      icon: <Search className="w-8 h-8 text-orange-400" />,
      title: "Universal Data Navigation",
      description: "Ask natural language questions across ALL company data sources—documents, calls, emails, financial records."
    },
    {
      id: 'workspaces',
      icon: <Building2 className="w-8 h-8 text-blue-400" />,
      title: "Intelligent Workspaces", 
      description: "Role-based access to company intelligence. Admins control what data sources and insights different team members can access."
    },
    {
      id: 'access',
      icon: <MessageSquare className="w-8 h-8 text-cyan-400" />,
      title: "Multi-Channel Access",
      description: "Get insights through existing workflows—Slack, email, web chat, or Claude Desktop."
    },
    {
      id: 'insights',
      icon: <Brain className="w-8 h-8 text-purple-400" />,
      title: "Real AI Insights",
      description: "Get intelligent analysis and patterns from your data, not just search results."
    },
    {
      id: 'integration',
      icon: <Zap className="w-8 h-8 text-yellow-400" />,
      title: "Seamless Integration",
      description: "Connect to existing data sources without disrupting workflows."
    },
    {
      id: 'security',
      icon: <Lock className="w-8 h-8 text-red-400" />,
      title: "Enterprise Security",
      description: "SOC 2 compliant with end-to-end encryption, audit logs, and granular permission controls."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-gray-900/98 backdrop-blur-md border-b border-gray-700/50 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <UruLogo size="lg" className="flex-shrink-0" />
              <span className="text-xl sm:text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 tracking-tight hover:from-uru-cyan-400 hover:to-uru-blue-400 transition-all duration-300 font-inter">
                Uru
              </span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-300 hover:text-uru-blue-400 transition-all duration-300 font-medium text-sm tracking-wide uppercase hover:scale-105 relative group cursor-pointer"
              >
                Features
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#demo"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('demo')?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-300 hover:text-uru-purple-400 transition-all duration-300 font-medium text-sm tracking-wide uppercase hover:scale-105 relative group cursor-pointer"
              >
                Demo
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-uru-purple-400 to-uru-blue-400 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#integrations"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('integrations')?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-300 hover:text-uru-cyan-400 transition-all duration-300 font-medium text-sm tracking-wide uppercase hover:scale-105 relative group cursor-pointer"
              >
                Integrations
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-uru-cyan-400 to-uru-blue-400 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#pricing"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('pricing')?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-300 hover:text-green-400 transition-all duration-300 font-medium text-sm tracking-wide uppercase hover:scale-105 relative group cursor-pointer"
              >
                Pricing
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-green-400 to-uru-cyan-400 transition-all duration-300 group-hover:w-full"></span>
              </a>
            </div>

            <div className="flex items-center space-x-3">
              <Link
                href="/login"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium px-3 sm:px-4 py-2 rounded-lg hover:bg-gray-800/50 text-sm sm:text-base"
              >
                Sign In
              </Link>
              <Link
                href="/request-access"
                className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-4 sm:px-6 py-2 sm:py-2.5 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform text-sm sm:text-base"
              >
                <span className="hidden sm:inline">Request Access</span>
                <span className="sm:hidden">Access</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-24 pb-16 min-h-screen flex items-center overflow-hidden">
        {/* Background gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-uru-blue-900/20 via-gray-900 to-uru-purple-900/20"></div>

        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-uru-blue-500/10 rounded-full blur-3xl animate-pulse-glow"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-uru-cyan-500/10 rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: '1s' }}></div>

        <div className="relative max-w-7xl mx-auto px-6 text-center w-full">
          <div className="max-w-5xl mx-auto">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-6 md:mb-8 leading-[1.2] tracking-tight animate-fade-in-up px-2 py-2">
              Navigate &
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 via-uru-cyan-400 to-uru-purple-400">
                {" "}Understand
              </span>
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-cyan-300 to-uru-blue-300">
                All Your Company Data
              </span>
            </h1>

            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-8 md:mb-10 leading-relaxed max-w-3xl mx-auto font-light animate-fade-in-up px-4 sm:px-0" style={{ animationDelay: '0.2s' }}>
              Enable your team to easily <span className="text-uru-cyan-400 font-medium">query</span>, <span className="text-uru-blue-400 font-medium">analyze</span>, and gain <span className="text-uru-purple-400 font-medium">insights</span> from ALL company data through
              AI—accessible via Slack, email, web chat, or Claude Desktop.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 animate-fade-in-up px-4 sm:px-0" style={{ animationDelay: '0.4s' }}>
              <Link
                href="/request-access"
                className="group bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-8 sm:px-10 py-3 sm:py-4 rounded-2xl font-semibold transition-all duration-300 flex items-center space-x-3 text-base sm:text-lg shadow-xl-colored hover:shadow-glow-blue hover:scale-105 transform w-full sm:w-auto justify-center"
              >
                <span>Request Access</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>

              <button
                onClick={handleWatchDemo}
                className="group border-2 border-uru-purple-500/50 hover:border-uru-purple-400 bg-uru-purple-500/10 hover:bg-uru-purple-500/20 text-uru-purple-300 hover:text-white px-8 sm:px-10 py-3 sm:py-4 rounded-2xl font-semibold transition-all duration-300 text-base sm:text-lg backdrop-blur-sm hover:shadow-glow-purple hover:scale-105 transform w-full sm:w-auto justify-center"
              >
                <span className="flex items-center space-x-3">
                  <span>Watch Demo</span>
                  <div className="w-3 h-3 bg-uru-purple-400 rounded-full animate-pulse"></div>
                </span>
              </button>
            </div>
          </div>
        </div>
      </section>



      {/* Why This Changes Everything Section */}
      <section id="features" className="py-32 relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Why This Changes
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-purple-400 to-uru-cyan-400 block mt-3">
                Everything
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
              Three core capabilities that transform how your business accesses and uses data
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-orange-500/30 hover:shadow-xl hover:shadow-orange-500/20 hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <Search className="w-12 h-12 text-orange-400 group-hover:text-orange-300" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-orange-100 transition-colors">
                Universal Search
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors">
                Find anything across all company systems with natural language queries. No more switching between tools or remembering where data lives.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-uru-blue-500/30 hover:shadow-xl-colored hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <MessageSquare className="w-12 h-12 text-uru-blue-400 group-hover:text-uru-blue-300" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-uru-blue-100 transition-colors">
                Workflow Native
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors">
                Access through tools you already use daily. No new interfaces to learn or workflows to change.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-uru-cyan-500/30 hover:shadow-xl hover:shadow-cyan-500/20 hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <Building2 className="w-12 h-12 text-uru-cyan-400 group-hover:text-uru-cyan-300" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-uru-cyan-100 transition-colors">
                Company-Specific
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors">
                AI tuned to your business context, terminology, and data patterns. Gets smarter as it learns your company.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Success Stories Section */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Trusted by Forward-Thinking
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-cyan-400 to-uru-blue-400 block mt-3">
                Companies
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
              See how businesses are transforming their operations with AI-powered data intelligence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Success Story 1 */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-blue-500/30 hover:shadow-xl-colored transition-all duration-300 group">
              <div className="mb-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-full flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">TechCorp Solutions</h3>
                    <p className="text-gray-400 text-sm">Software Development</p>
                  </div>
                </div>
                <blockquote className="text-gray-300 leading-relaxed mb-4 group-hover:text-gray-200 transition-colors">
                  "Uru transformed how we access project data. Instead of hunting through Slack, emails, and docs, our team gets instant answers about any project status or client requirement."
                </blockquote>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>40% faster project queries</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Success Story 2 */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20 transition-all duration-300 group">
              <div className="mb-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-full flex items-center justify-center">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Growth Dynamics</h3>
                    <p className="text-gray-400 text-sm">Marketing Agency</p>
                  </div>
                </div>
                <blockquote className="text-gray-300 leading-relaxed mb-4 group-hover:text-gray-200 transition-colors">
                  "Our account managers can now instantly pull client performance data, campaign insights, and historical context without switching between 8 different tools."
                </blockquote>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>60% reduction in data lookup time</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Success Story 3 */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-cyan-500/30 hover:shadow-xl hover:shadow-cyan-500/20 transition-all duration-300 group">
              <div className="mb-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-uru-cyan-600 to-green-600 rounded-full flex items-center justify-center">
                    <Brain className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Meridian Consulting</h3>
                    <p className="text-gray-400 text-sm">Business Consulting</p>
                  </div>
                </div>
                <blockquote className="text-gray-300 leading-relaxed mb-4 group-hover:text-gray-200 transition-colors">
                  "Uru gives us a competitive edge. We can analyze client patterns across all touchpoints and provide insights that would take weeks to compile manually."
                </blockquote>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>3x faster client analysis</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <Link
              href="/case-studies"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
            >
              <span>View All Case Studies</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Platform Preview Section */}
      <section id="demo" className="py-32 bg-gradient-to-b from-gray-800/30 via-gray-900/50 to-gray-800/30 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-uru-blue-900/5 to-uru-purple-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-16 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              See Uru in
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-cyan-400 to-uru-blue-400 block mt-3">
                Action
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light mb-12">
              Experience real platform capabilities with actual interface screenshots and examples of automated reports in action.
            </p>
          </div>

          {/* Real Chat Interface Screenshot */}
          <div className="max-w-6xl mx-auto mb-16">
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-700/50 shadow-xl-colored">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">Real Chat Interface in Action</h3>
              <div className="bg-gray-900/80 rounded-2xl p-6 mb-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-gray-400 text-sm ml-4">Uru AI Assistant - Live Interface</span>
                </div>

                {/* Enhanced Sample Conversation */}
                <div className="space-y-4">
                  <div className="flex justify-end">
                    <div className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 text-white px-6 py-3 rounded-2xl rounded-br-md max-w-md">
                      <p className="text-sm">Find all client calls mentioning cash flow issues and generate a summary report</p>
                    </div>
                  </div>
                  <div className="flex justify-start">
                    <div className="bg-gray-700/80 text-white px-6 py-3 rounded-2xl rounded-bl-md max-w-lg">
                      <p className="text-sm mb-3">Found 23 calls across 8 clients. Generating comprehensive analysis...</p>
                      <div className="bg-gray-800/60 rounded-lg p-3 mb-3">
                        <p className="text-xs text-uru-cyan-300 font-semibold mb-2">📊 AUTOMATED REPORT GENERATED</p>
                        <ul className="text-xs text-gray-300 space-y-1">
                          <li>• 15 calls from Q3 2024 showing seasonal trends</li>
                          <li>• 3 clients mentioned delayed payments from major accounts</li>
                          <li>• 2 clients discussed expansion funding needs</li>
                          <li>• Recommended follow-up actions identified</li>
                        </ul>
                      </div>
                      <p className="text-xs text-uru-blue-300">Report saved to company knowledge base and scheduled for weekly updates.</p>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <div className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 text-white px-6 py-3 rounded-2xl rounded-br-md max-w-md">
                      <p className="text-sm">Set up automated weekly cash flow monitoring for all clients</p>
                    </div>
                  </div>
                  <div className="flex justify-start">
                    <div className="bg-gray-700/80 text-white px-6 py-3 rounded-2xl rounded-bl-md max-w-lg">
                      <p className="text-sm mb-2">✅ Automated monitoring configured:</p>
                      <ul className="text-xs text-gray-300 space-y-1">
                        <li>• Weekly analysis of client communications</li>
                        <li>• Cash flow keyword detection across all channels</li>
                        <li>• Automatic report generation every Monday</li>
                        <li>• Alert system for urgent cash flow mentions</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Multi-channel Access Badges */}
              <div className="flex flex-wrap justify-center gap-4 mb-6">
                <span className="bg-uru-blue-500/20 text-uru-blue-300 px-4 py-2 rounded-full font-medium border border-uru-blue-500/30 flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4" />
                  <span>Slack</span>
                </span>
                <span className="bg-uru-purple-500/20 text-uru-purple-300 px-4 py-2 rounded-full font-medium border border-uru-purple-500/30 flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>Email</span>
                </span>
                <span className="bg-green-500/20 text-green-300 px-4 py-2 rounded-full font-medium border border-green-500/30 flex items-center space-x-2">
                  <Brain className="w-4 h-4" />
                  <span>Web Chat</span>
                </span>
                <span className="bg-uru-cyan-500/20 text-uru-cyan-300 px-4 py-2 rounded-full font-medium border border-uru-cyan-500/30 flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Claude Desktop</span>
                </span>
              </div>
            </div>
          </div>

          {/* Automated Reports Examples */}
          <div className="max-w-6xl mx-auto mb-16">
            <h3 className="text-3xl font-bold text-white mb-8 text-center">Automated Reports Generated by the Platform</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Weekly Business Intelligence Report */}
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-lg">📈</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">Weekly Business Intelligence</h4>
                    <p className="text-gray-400 text-sm">Auto-generated every Monday</p>
                  </div>
                </div>
                <div className="bg-gray-900/60 rounded-lg p-4 text-sm">
                  <div className="text-uru-cyan-300 font-semibold mb-2">Key Insights This Week:</div>
                  <ul className="text-gray-300 space-y-1 text-xs">
                    <li>• 23% increase in client inquiries vs last week</li>
                    <li>• 3 new prospects identified from email analysis</li>
                    <li>• Cash flow concerns mentioned by 2 clients</li>
                    <li>• 5 follow-up actions recommended</li>
                  </ul>
                  <div className="mt-3 pt-3 border-t border-gray-700">
                    <p className="text-xs text-gray-400">Sources: Gmail, Calendar, Drive, Slack</p>
                  </div>
                </div>
              </div>

              {/* Client Relationship Summary */}
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-lg">👥</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">Client Relationship Summary</h4>
                    <p className="text-gray-400 text-sm">Monthly comprehensive analysis</p>
                  </div>
                </div>
                <div className="bg-gray-900/60 rounded-lg p-4 text-sm">
                  <div className="text-uru-purple-300 font-semibold mb-2">Relationship Health Score:</div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-xs">TechCorp Solutions</span>
                      <span className="text-green-400 text-xs font-semibold">92% Healthy</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-xs">DataFlow Inc</span>
                      <span className="text-yellow-400 text-xs font-semibold">76% Attention Needed</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-xs">CloudSync Ltd</span>
                      <span className="text-green-400 text-xs font-semibold">88% Healthy</span>
                    </div>
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-700">
                    <p className="text-xs text-gray-400">Based on communication frequency, sentiment, project status</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={handleWatchDemo}
              className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform text-lg"
            >
              See Full Platform Demo
            </button>
          </div>
        </div>
      </section>

      {/* Access Control & User Management Section */}
      <section className="py-32 bg-gradient-to-b from-gray-900/30 via-gray-800/50 to-gray-900/30 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-uru-purple-900/5 to-uru-blue-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-16 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Enterprise-Grade
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-purple-400 to-uru-blue-400 block mt-3">
                Access Control
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light mb-12">
              Control access to company-specific AI capabilities and proprietary data sources.
              Personal integrations like Gmail and Calendar are always available - administrators manage access to custom business intelligence tools.
            </p>
          </div>

          {/* Access Control Dashboard Screenshot */}
          <div className="max-w-6xl mx-auto mb-16">
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-700/50 shadow-xl-colored">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">Live Access Control Dashboard</h3>

              {/* Simulated Dashboard Interface */}
              <div className="bg-gray-900/80 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-lg flex items-center justify-center">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-white font-semibold">Permission Management</h4>
                  </div>
                  <span className="text-uru-cyan-400 text-sm">12 team members • 6 company tools</span>
                </div>

                {/* Team Member List */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left Column - Team Members */}
                  <div className="space-y-4">
                    <h5 className="text-uru-purple-300 font-semibold text-sm mb-3">TEAM MEMBERS</h5>

                    {/* Team Member 1 */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                            JS
                          </div>
                          <div>
                            <p className="text-white font-medium text-sm">John Smith</p>
                            <p className="text-gray-400 text-xs">Admin</p>
                          </div>
                        </div>
                        <span className="text-green-400 text-xs font-semibold">Full Access</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Transcript Search</span>
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Client Database</span>
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Knowledge Base</span>
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Custom RAG</span>
                      </div>
                    </div>

                    {/* Team Member 2 */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                            MJ
                          </div>
                          <div>
                            <p className="text-white font-medium text-sm">Maria Johnson</p>
                            <p className="text-gray-400 text-xs">Member</p>
                          </div>
                        </div>
                        <span className="text-yellow-400 text-xs font-semibold">Limited Access</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Transcript Search</span>
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">Knowledge Base</span>
                        <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded text-xs">Client DB (Read Only)</span>
                        <span className="bg-gray-500/20 text-gray-400 px-2 py-1 rounded text-xs">Custom RAG (Disabled)</span>
                      </div>
                    </div>

                    {/* Team Member 3 */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                            DW
                          </div>
                          <div>
                            <p className="text-white font-medium text-sm">David Wilson</p>
                            <p className="text-gray-400 text-xs">Viewer</p>
                          </div>
                        </div>
                        <span className="text-blue-400 text-xs font-semibold">View Only</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-xs">Knowledge Base (View)</span>
                        <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-xs">Doc Repository (View)</span>
                        <span className="bg-gray-500/20 text-gray-400 px-2 py-1 rounded text-xs">Client DB (Disabled)</span>
                        <span className="bg-gray-500/20 text-gray-400 px-2 py-1 rounded text-xs">Custom RAG (Disabled)</span>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Permission Details */}
                  <div className="space-y-4">
                    <h5 className="text-uru-blue-300 font-semibold text-sm mb-3">PERMISSION DETAILS</h5>

                    {/* Transcript Search Permissions */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm">🎙️</span>
                        </div>
                        <h6 className="text-white font-semibold">Transcript Search</h6>
                      </div>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-300">Search Transcripts</span>
                          <span className="text-green-400">✓ Enabled</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Export Results</span>
                          <span className="text-yellow-400">⚠ Member+</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Delete Transcripts</span>
                          <span className="text-red-400">✗ Admin Only</span>
                        </div>
                      </div>
                    </div>

                    {/* Client Database Permissions */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm">👥</span>
                        </div>
                        <h6 className="text-white font-semibold">Client Database</h6>
                      </div>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-300">View Client Data</span>
                          <span className="text-green-400">✓ Enabled</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Edit Client Records</span>
                          <span className="text-yellow-400">⚠ Member+</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Delete Records</span>
                          <span className="text-red-400">✗ Admin Only</span>
                        </div>
                      </div>
                    </div>

                    {/* Custom RAG Permissions */}
                    <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm">🧠</span>
                        </div>
                        <h6 className="text-white font-semibold">Custom RAG Sources</h6>
                      </div>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-300">Query RAG Data</span>
                          <span className="text-green-400">✓ Enabled</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Upload Documents</span>
                          <span className="text-yellow-400">⚠ Member+</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Manage Data Sources</span>
                          <span className="text-red-400">✗ Admin Only</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Key Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {/* Granular Permissions */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20 hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Granular Permissions</h3>
              <p className="text-gray-300 text-sm leading-relaxed mb-4 text-center">
                Control access to company-specific AI tools and proprietary data sources with granular permission levels.
              </p>
              <ul className="space-y-2 text-xs text-gray-300">
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-purple-400 flex-shrink-0" />
                  <span>Custom RAG data source control</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-purple-400 flex-shrink-0" />
                  <span>Client database access levels</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-purple-400 flex-shrink-0" />
                  <span>Transcript search permissions</span>
                </li>
              </ul>
            </div>

            {/* Real-time Monitoring */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-blue-500/30 hover:shadow-xl-colored hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Real-time Monitoring</h3>
              <p className="text-gray-300 text-sm leading-relaxed mb-4 text-center">
                Monitor usage of company AI tools and proprietary data access with comprehensive audit trails.
              </p>
              <ul className="space-y-2 text-xs text-gray-300">
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-blue-400 flex-shrink-0" />
                  <span>Company data access tracking</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-blue-400 flex-shrink-0" />
                  <span>Custom tool usage analytics</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-blue-400 flex-shrink-0" />
                  <span>Proprietary data query logs</span>
                </li>
              </ul>
            </div>

            {/* Easy Management */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-uru-cyan-500/30 hover:shadow-xl hover:shadow-cyan-500/20 hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-uru-cyan-600 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Easy Management</h3>
              <p className="text-gray-300 text-sm leading-relaxed mb-4 text-center">
                Simple interface for managing access to company-specific AI capabilities and business intelligence tools.
              </p>
              <ul className="space-y-2 text-xs text-gray-300">
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-cyan-400 flex-shrink-0" />
                  <span>Role-based permission templates</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-cyan-400 flex-shrink-0" />
                  <span>Custom data source management</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-uru-cyan-400 flex-shrink-0" />
                  <span>Business tool access control</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={() => scrollToSection('pricing')}
              className="bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 hover:from-uru-purple-700 hover:to-uru-blue-700 text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-purple hover:scale-105 transform text-lg"
            >
              Explore Access Control Features
            </button>
          </div>
        </div>
      </section>

      {/* Automated Reports & Company Intelligence Section */}
      <section className="py-32 bg-gradient-to-b from-gray-800/30 via-gray-900/50 to-gray-800/30 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-uru-blue-900/5 to-uru-purple-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Automated Reports &
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-3">
                Company Memory
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
              Generate reports automatically and maintain comprehensive company context that's always accessible through your AI interface.
            </p>
          </div>

          {/* Content blocks - 3-column grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-16">
            {/* Scheduled Reports */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-uru-blue-500/30 hover:shadow-xl-colored hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-3xl">📊</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-uru-blue-100 transition-colors text-center">
                Scheduled Reports
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors mb-6 text-center">
                Set up reports to generate automatically—weekly summaries, monthly updates, quarterly reviews. Configure once, receive consistently.
              </p>
              <ul className="space-y-3 text-gray-300 group-hover:text-gray-200 transition-colors">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-blue-400 flex-shrink-0" />
                  <span>Daily, weekly, monthly schedules</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-blue-400 flex-shrink-0" />
                  <span>Custom templates & formatting</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-blue-400 flex-shrink-0" />
                  <span>Multi-source data compilation</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-blue-400 flex-shrink-0" />
                  <span>Direct delivery options</span>
                </li>
              </ul>
            </div>

            {/* Searchable Company Knowledge */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-uru-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20 hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-3xl">🧠</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-uru-purple-100 transition-colors text-center">
                Searchable Company Knowledge
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors mb-6 text-center">
                Maintain comprehensive, searchable records of clients, projects, decisions, and processes. Every detail accessible through natural language queries.
              </p>
              <ul className="space-y-3 text-gray-300 group-hover:text-gray-200 transition-colors">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-purple-400 flex-shrink-0" />
                  <span>Natural language search</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-purple-400 flex-shrink-0" />
                  <span>Relationship mapping</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-purple-400 flex-shrink-0" />
                  <span>Historical tracking</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-purple-400 flex-shrink-0" />
                  <span>Cross-reference capabilities</span>
                </li>
              </ul>
            </div>

            {/* Reusable Analysis Templates */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 group border border-gray-700/50 hover:border-uru-cyan-500/30 hover:shadow-xl hover:shadow-cyan-500/20 hover:scale-105 transform">
              <div className="mb-8 group-hover:scale-110 transition-transform duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-cyan-600 to-green-600 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-3xl">💾</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-uru-cyan-100 transition-colors text-center">
                Reusable Analysis Templates
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg group-hover:text-gray-200 transition-colors mb-6 text-center">
                Save complex business queries as templates. Turn sophisticated analysis into simple, repeatable workflows.
              </p>
              <ul className="space-y-3 text-gray-300 group-hover:text-gray-200 transition-colors">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-cyan-400 flex-shrink-0" />
                  <span>Saved query templates</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-cyan-400 flex-shrink-0" />
                  <span>One-click execution</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-cyan-400 flex-shrink-0" />
                  <span>Custom analysis workflows</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-uru-cyan-400 flex-shrink-0" />
                  <span>Template sharing</span>
                </li>
              </ul>
            </div>
          </div>

          {/* CTA Button */}
          <div className="text-center">
            <button
              onClick={handleWatchDemo}
              className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform text-lg"
            >
              See Automated Reporting Demo
            </button>
          </div>
        </div>
      </section>

      {/* Integration Ecosystem Section */}
      <section id="integrations" className="py-32 bg-gradient-to-b from-gray-800/30 via-gray-800/50 to-gray-900/30 relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-uru-blue-900/5 to-uru-purple-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Connect Your Entire
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-cyan-400 to-uru-blue-400 block mt-3">
                Tech Stack
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto mb-10 leading-relaxed font-light">
              <span className="text-uru-cyan-400 font-medium">Personal integrations</span> start immediately out of the box.
              <span className="text-uru-blue-400 font-medium"> Company-wide tools</span> added through consultation.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm md:text-base">
              <span className="bg-uru-blue-500/20 text-uru-blue-300 px-4 py-2 rounded-full font-medium border border-uru-blue-500/30 hover:bg-uru-blue-500/30 transition-colors">Google Workspace</span>
              <span className="bg-uru-purple-500/20 text-uru-purple-300 px-4 py-2 rounded-full font-medium border border-uru-purple-500/30 hover:bg-uru-purple-500/30 transition-colors">Communication</span>
              <span className="bg-green-500/20 text-green-300 px-4 py-2 rounded-full font-medium border border-green-500/30 hover:bg-green-500/30 transition-colors">Productivity</span>
              <span className="bg-orange-500/20 text-orange-300 px-4 py-2 rounded-full font-medium border border-orange-500/30 hover:bg-orange-500/30 transition-colors">Development</span>
              <span className="bg-uru-cyan-500/20 text-uru-cyan-300 px-4 py-2 rounded-full font-medium border border-uru-cyan-500/30 hover:bg-uru-cyan-500/30 transition-colors">Storage</span>
            </div>
          </div>

          <div className="tech-stack-grid-container max-w-7xl mx-auto px-4">
            {/* First row - 8 integrations */}
            <div className="tech-stack-grid grid grid-cols-8 gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-4 sm:mb-6 md:mb-8">
              {integrations.slice(0, 8).map((integration) => (
                <div
                  key={integration.id}
                  className="tech-stack-item relative bg-gray-800/50 backdrop-blur-sm rounded-xl hover:bg-gray-700/60 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 group border border-gray-700/50 hover:border-blue-500/30 cursor-pointer aspect-square w-full mx-auto flex flex-col"
                >
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />

                  {/* Simplified card content - logo and name only */}
                  <div className="relative flex flex-col h-full p-3 sm:p-4 md:p-5 lg:p-6 justify-center items-center">
                    {/* Significantly larger logo container */}
                    <div className="tech-stack-icon-container w-8 h-8 sm:w-12 sm:h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 xl:w-24 xl:h-24 mb-2 sm:mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                      <ServiceIcon integration={integration} />
                    </div>

                    {/* Larger company name text */}
                    <h3 className="text-white font-semibold text-sm sm:text-base md:text-lg lg:text-xl group-hover:text-blue-100 transition-colors leading-tight text-center line-clamp-2">
                      {integration.name}
                    </h3>
                  </div>
                </div>
              ))}
            </div>

            {/* Second row - 8 integrations */}
            <div className="tech-stack-grid grid grid-cols-8 gap-3 sm:gap-4 md:gap-6 lg:gap-8">
              {integrations.slice(8, 16).map((integration) => (
                <div
                  key={integration.id}
                  className="tech-stack-item relative bg-gray-800/50 backdrop-blur-sm rounded-xl hover:bg-gray-700/60 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 group border border-gray-700/50 hover:border-blue-500/30 cursor-pointer aspect-square w-full mx-auto flex flex-col"
                >
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />

                  {/* Simplified card content - logo and name only */}
                  <div className="relative flex flex-col h-full p-3 sm:p-4 md:p-5 lg:p-6 justify-center items-center">
                    {/* Significantly larger logo container */}
                    <div className="tech-stack-icon-container w-8 h-8 sm:w-12 sm:h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 xl:w-24 xl:h-24 mb-2 sm:mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                      <ServiceIcon integration={integration} />
                    </div>

                    {/* Larger company name text */}
                    <h3 className="text-white font-semibold text-sm sm:text-base md:text-lg lg:text-xl group-hover:text-blue-100 transition-colors leading-tight text-center line-clamp-2">
                      {integration.name}
                    </h3>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="text-center mt-16">
            <Link
              href="/integrations"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
            >
              <span>View All Integrations</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Market Positioning Section */}
      <section className="py-32 bg-gradient-to-b from-gray-800/30 via-gray-900/50 to-gray-800/30 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-uru-purple-900/5 to-uru-cyan-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Why Most AI Solutions
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-purple-400 to-uru-cyan-400 block mt-3">
                Miss the Mark
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
              The AI market has a gap between expensive enterprise solutions and limited consumer tools.
              <span className="text-uru-cyan-400 font-medium"> Uru bridges this gap</span> for growing businesses.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {/* Enterprise Solutions */}
            <div className="bg-gradient-to-br from-red-900/20 to-red-800/20 backdrop-blur-sm rounded-2xl p-8 border border-red-700/30 text-center hover:scale-105 transition-transform duration-300">
              <div className="w-16 h-16 bg-red-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                <Building2 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-red-300 mb-6">Enterprise Solutions</h3>
              <div className="space-y-4 text-gray-300">
                <div className="bg-red-900/30 rounded-lg p-4">
                  <p className="text-lg font-semibold text-red-400 mb-2">$50K+ minimum investment</p>
                  <p className="text-sm">Often requires 6-figure commitments</p>
                </div>
                <ul className="space-y-2 text-left">
                  <li>• 6-18 month implementation cycles</li>
                  <li>• Requires dedicated IT teams</li>
                  <li>• Complex integration processes</li>
                  <li>• Over-engineered for SMB needs</li>
                  <li>• Vendor lock-in with rigid contracts</li>
                </ul>
              </div>
              <div className="mt-6 text-red-400 font-medium">
                Built for Fortune 500, not growing businesses
              </div>
            </div>

            {/* Consumer Tools */}
            <div className="bg-gradient-to-br from-yellow-900/20 to-yellow-800/20 backdrop-blur-sm rounded-2xl p-8 border border-yellow-700/30 text-center hover:scale-105 transition-transform duration-300">
              <div className="w-16 h-16 bg-yellow-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                <MessageSquare className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-yellow-300 mb-6">Consumer AI Tools</h3>
              <div className="space-y-4 text-gray-300">
                <div className="bg-yellow-900/30 rounded-lg p-4">
                  <p className="text-lg font-semibold text-yellow-400 mb-2">$20-100/month per user</p>
                  <p className="text-sm">Affordable but severely limited</p>
                </div>
                <ul className="space-y-2 text-left">
                  <li>• No access to company data</li>
                  <li>• Generic, context-free responses</li>
                  <li>• Security and privacy concerns</li>
                  <li>• No workflow integration</li>
                  <li>• Can't learn your business</li>
                </ul>
              </div>
              <div className="mt-6 text-yellow-400 font-medium">
                Great for personal use, inadequate for business
              </div>
            </div>

            {/* Uru Platform */}
            <div className="bg-gradient-to-br from-uru-blue-900/30 to-uru-cyan-900/30 backdrop-blur-sm rounded-2xl p-8 border border-uru-blue-500/50 text-center relative overflow-hidden hover:scale-105 transition-transform duration-300">
              <div className="absolute inset-0 bg-gradient-to-br from-uru-blue-500/10 to-uru-cyan-500/10"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-uru-blue-300 mb-6">Uru Platform</h3>
                <div className="space-y-4 text-gray-200">
                  <div className="bg-uru-blue-900/30 rounded-lg p-4 border border-uru-blue-500/30">
                    <p className="text-lg font-semibold text-uru-cyan-400 mb-2">SMB-focused approach</p>
                    <p className="text-sm">Right-sized for growing companies</p>
                  </div>
                  <ul className="space-y-2 text-left">
                    <li>• Personal tools: Immediate access</li>
                    <li>• Company tools: 2-8 week setup</li>
                    <li>• Full business data integration</li>
                    <li>• Learns your company context</li>
                    <li>• Works in your existing tools</li>
                  </ul>
                </div>
                <div className="mt-6 text-center">
                  <span className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 text-white px-6 py-3 rounded-full text-sm font-semibold inline-block">
                    Perfect for $1M-$50M Revenue Companies
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* The Gap Explanation */}
          <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-12 border border-gray-700/50 text-center">
            <h3 className="text-3xl font-bold text-white mb-6">The Missing Middle</h3>
            <p className="text-xl text-gray-200 leading-relaxed max-w-4xl mx-auto mb-8">
              Growing businesses need more than consumer tools but can't justify enterprise costs.
              <span className="text-uru-cyan-400 font-semibold"> Uru fills this gap</span> with business-grade AI
              that's accessible, practical, and designed for companies ready to scale.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="bg-gray-900/50 rounded-xl p-6">
                <div className="text-2xl font-bold text-uru-blue-400 mb-2">Enterprise-Grade</div>
                <div className="text-gray-300">Security, integration, and intelligence</div>
              </div>
              <div className="bg-gray-900/50 rounded-xl p-6">
                <div className="text-2xl font-bold text-uru-cyan-400 mb-2">SMB-Friendly</div>
                <div className="text-gray-300">Pricing, timeline, and complexity</div>
              </div>
              <div className="bg-gray-900/50 rounded-xl p-6">
                <div className="text-2xl font-bold text-uru-purple-400 mb-2">Immediate Value</div>
                <div className="text-gray-300">Personal tools work day one</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Process Section */}
      <section className="py-32 bg-gradient-to-b from-gray-800/30 via-gray-900/50 to-gray-800/30 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-uru-purple-900/5 to-uru-blue-900/5"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              How It
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-3">
                Works
              </span>
            </h2>
            <p className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
              <span className="text-green-400 font-medium">Personal integrations start immediately.</span>
              Company-wide tools are added through our consultation process.
            </p>
          </div>

          {/* Immediate Access Banner */}
          <div className="bg-gradient-to-r from-green-900/20 to-uru-blue-900/20 backdrop-blur-sm rounded-2xl p-8 border border-green-500/30 text-center mb-16">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <Zap className="w-8 h-8 text-green-400" />
              <h3 className="text-2xl font-bold text-green-300">Immediate Access</h3>
              <Zap className="w-8 h-8 text-green-400" />
            </div>
            <p className="text-gray-200 text-lg max-w-3xl mx-auto">
              Your team can start using personal AI tools (Gmail, Drive, Calendar)
              <span className="text-green-400 font-semibold"> out of the box on day one</span>.
              Company-wide integrations are added through our consultation process below.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
            {/* Connecting lines for desktop - improved positioning */}
            <div className="hidden lg:block absolute top-16 left-[12.5%] right-[12.5%] h-0.5 bg-gradient-to-r from-uru-blue-500/30 to-uru-cyan-500/30 z-0"></div>

            {/* Step 1 */}
            <div className="relative text-center group z-10">
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 border border-gray-700/50 hover:border-uru-blue-500/30 hover:shadow-xl-colored hover:scale-105 transform">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl group-hover:scale-110 transition-transform">
                  1
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Discovery</h3>
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  Assess company data sources and integration needs
                </p>
                <span className="text-uru-blue-400 text-xs font-medium">2-4 weeks for company tools</span>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative text-center group z-10">
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 border border-gray-700/50 hover:border-uru-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20 hover:scale-105 transform">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl group-hover:scale-110 transition-transform">
                  2
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Custom Build</h3>
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  Develop AI systems tailored to your business data
                </p>
                <span className="text-uru-purple-400 text-xs font-medium">4-8 weeks for company tools</span>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative text-center group z-10">
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 border border-gray-700/50 hover:border-uru-cyan-500/30 hover:shadow-xl hover:shadow-cyan-500/20 hover:scale-105 transform">
                <div className="w-16 h-16 bg-gradient-to-r from-uru-cyan-600 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl group-hover:scale-110 transition-transform">
                  3
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Integration</h3>
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  Connect company tools to existing workflows
                </p>
                <span className="text-uru-cyan-400 text-xs font-medium">1-2 weeks for company tools</span>
              </div>
            </div>

            {/* Step 4 */}
            <div className="relative text-center group z-10">
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-8 hover:from-gray-700/80 hover:to-gray-800/80 transition-all duration-300 border border-gray-700/50 hover:border-green-500/30 hover:shadow-xl hover:shadow-green-500/20 hover:scale-105 transform">
                <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-uru-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl group-hover:scale-110 transition-transform">
                  4
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Scale & Optimize</h3>
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  Continuous improvement and expansion
                </p>
                <span className="text-green-400 text-xs font-medium">Ongoing support</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>

        <div className="relative max-w-4xl mx-auto px-6">
          <div className="text-center mb-20 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Frequently Asked
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-purple-400 to-uru-cyan-400 block mt-3">
                Questions
              </span>
            </h2>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "How is this different from ChatGPT for business?",
                answer: "Unlike generic AI tools, Uru is specifically trained on YOUR company data and integrated into YOUR workflows. It understands your business context, terminology, and can access all your proprietary information securely."
              },
              {
                question: "What data sources can you connect?",
                answer: "We integrate with 16+ business tools including Google Workspace, Microsoft 365, Slack, Salesforce, QuickBooks, and more. Our team conducts a comprehensive audit to identify all relevant data sources during the discovery phase."
              },
              {
                question: "How long does implementation take?",
                answer: "Total implementation typically takes 6-12 weeks: 2-4 weeks for discovery and assessment, 4-8 weeks for custom development and integration. We work closely with your team to minimize disruption."
              },
              {
                question: "Is our data secure and private?",
                answer: "Absolutely. We're SOC 2 compliant with end-to-end encryption, audit logs, and granular permission controls. Your data never leaves your secure environment and is never used to train other models."
              },
              {
                question: "Can we access through our existing tools?",
                answer: "Yes! That's a core feature. Access Uru through Slack, email, web chat, Claude Desktop, or any tool your team already uses. No need to change workflows or learn new interfaces."
              },
              {
                question: "What ongoing support is provided?",
                answer: "We provide 24/7 technical support, regular model updates, performance monitoring, and continuous optimization. Our team ensures your AI intelligence platform evolves with your business needs."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl border border-gray-700/50 overflow-hidden">
                <details className="group">
                  <summary className="flex items-center justify-between p-8 cursor-pointer hover:bg-gray-700/30 transition-colors">
                    <h3 className="text-xl font-semibold text-white group-hover:text-uru-blue-100 transition-colors pr-4">
                      {faq.question}
                    </h3>
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-uru-blue-600 flex items-center justify-center group-hover:bg-uru-blue-500 transition-colors">
                      <span className="text-white text-sm font-bold group-open:rotate-45 transition-transform">+</span>
                    </div>
                  </summary>
                  <div className="px-8 pb-8">
                    <p className="text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </details>
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* Final CTA Section */}
      <section id="pricing" className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="text-center mb-16 section-heading-container">
            <h2 className="section-heading text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight">
              Ready to Transform Your
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-3">
                Business Intelligence?
              </span>
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
            {/* Contact Form */}
            <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 border border-gray-700/50">
              <h3 className="text-2xl font-bold text-white mb-8">Get Started Today</h3>
              <form className="space-y-6">
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">Name</label>
                  <input
                    type="text"
                    className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-uru-blue-500 focus:outline-none transition-colors"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">Email</label>
                  <input
                    type="email"
                    className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-uru-blue-500 focus:outline-none transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">Company</label>
                  <input
                    type="text"
                    className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-uru-blue-500 focus:outline-none transition-colors"
                    placeholder="Company name"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">Use Case</label>
                  <textarea
                    rows={4}
                    className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-uru-blue-500 focus:outline-none transition-colors resize-none"
                    placeholder="Describe your main use case or challenge..."
                  ></textarea>
                </div>
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
                >
                  Get Started Today
                </button>
              </form>
            </div>

            {/* Alternative Contact Methods */}
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 border border-gray-700/50">
                <h3 className="text-2xl font-bold text-white mb-6">Schedule Discovery Call</h3>
                <p className="text-gray-300 leading-relaxed mb-8">
                  Prefer to talk? Schedule a 30-minute discovery call to discuss your specific needs and see if Uru is the right fit for your business.
                </p>
                <button
                  onClick={handleScheduleDemo}
                  className="w-full bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 hover:from-uru-purple-700 hover:to-uru-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-purple hover:scale-105 transform"
                >
                  Schedule Discovery Call
                </button>
              </div>

              <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-10 border border-gray-700/50">
                <h3 className="text-xl font-bold text-white mb-6">Other Ways to Connect</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 text-gray-300">
                    <div className="w-8 h-8 bg-uru-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">@</span>
                    </div>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-300">
                    <div className="w-8 h-8 bg-uru-cyan-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">#</span>
                    </div>
                    <span>LinkedIn: /company/uru-enterprises</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-300">
                    <div className="w-8 h-8 bg-uru-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">📅</span>
                    </div>
                    <span>Calendly: calendly.com/uru-discovery</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Modal */}
      {showDemoModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 max-w-2xl w-full border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">Platform Demo</h3>
              <button
                onClick={() => setShowDemoModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            <div className="space-y-6">
              <p className="text-gray-300 leading-relaxed">
                Experience Uru's AI-powered business intelligence platform in action. Choose how you'd like to see the demo:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => {
                    setShowDemoModal(false);
                    scrollToSection('demo');
                  }}
                  className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-6 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
                >
                  View Interactive Demo
                </button>
                <button
                  onClick={() => {
                    setShowDemoModal(false);
                    handleScheduleDemo();
                  }}
                  className="bg-gradient-to-r from-uru-purple-600 to-uru-blue-600 hover:from-uru-purple-700 hover:to-uru-blue-700 text-white px-6 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-purple hover:scale-105 transform"
                >
                  Schedule Live Demo
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contact Modal */}
      {showContactModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 max-w-md w-full border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">Contact Us</h3>
              <button
                onClick={() => setShowContactModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-300 leading-relaxed mb-6">
                Ready to transform your business intelligence? Get in touch with us:
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-uru-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">@</span>
                  </div>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-uru-cyan-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">#</span>
                  </div>
                  <span>LinkedIn: /company/uru-enterprises</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-uru-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">📅</span>
                  </div>
                  <span>Calendly: calendly.com/uru-discovery</span>
                </div>
              </div>
              <div className="pt-4">
                <button
                  onClick={() => {
                    setShowContactModal(false);
                    scrollToSection('pricing');
                  }}
                  className="w-full bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
                >
                  Get Started
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Prevent static generation for this page to avoid SSR issues
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
