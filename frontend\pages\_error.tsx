import React from 'react';
import Link from 'next/link';
import { UruLogo } from '../components/shared/UruLogo';
import { ArrowLeft, RefreshCw } from 'lucide-react';

interface ErrorProps {
  statusCode?: number;
  hasGetInitialPropsRun?: boolean;
  err?: Error;
}

function Error({ statusCode }: ErrorProps) {
  const is404 = statusCode === 404;
  const is500 = statusCode === 500;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <UruLogo size="lg" />
        </div>

        {/* Error Content */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700">
          <h1 className="text-6xl font-bold text-white mb-4">
            {statusCode || '???'}
          </h1>
          
          <h2 className="text-2xl font-semibold text-white mb-4">
            {is404 ? 'Page Not Found' : is500 ? 'Server Error' : 'Something went wrong'}
          </h2>
          
          <p className="text-gray-400 mb-8">
            {is404 
              ? "The page you're looking for doesn't exist or has been moved."
              : is500
              ? "We're experiencing technical difficulties. Please try again later."
              : "An unexpected error occurred. Please try again."
            }
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Home
            </Link>
            
            {!is404 && (
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>
            )}
          </div>
        </div>

        {/* Footer */}
        <p className="text-gray-500 text-sm mt-8">
          Need help? Contact our support team.
        </p>
      </div>
    </div>
  );
}

Error.getInitialProps = ({ res, err }: { res?: any; err?: any }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error;
