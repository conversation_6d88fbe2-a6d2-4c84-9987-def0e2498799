import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { Sidebar } from '../../components/shared/Sidebar';
import { ProtectedRoute } from '../../components/auth/ProtectedRoute';
import { useAuth } from '../../components/auth/AuthContext';
import { apiService } from '../../utils/api';

import { IntegrationHub } from '../../components/integrations/IntegrationHub';
import { Settings, User, Bell, Shield, Database, Zap, ExternalLink, CheckCircle, XCircle, Monitor, Copy, Download, RefreshCw } from 'lucide-react';

export default function SettingsPage() {
  const { employee, logout } = useAuth();
  const router = useRouter();
  const [oauthStatus, setOauthStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [mcpConfig, setMcpConfig] = useState<any>(null);
  const [mcpLoading, setMcpLoading] = useState(false);
  const [serviceConnections, setServiceConnections] = useState<{[key: string]: boolean}>({
    gmail: false,
    drive: false,
    calendar: false
  });
  const [connectingService, setConnectingService] = useState<string | null>(null);

  // Load OAuth status on mount and handle OAuth callback
  useEffect(() => {
    loadOAuthStatus();
    checkServiceConnections();
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const oauthSuccess = urlParams.get('oauth_success');
    const oauthError = urlParams.get('oauth_error');
    const error = urlParams.get('error');

    if (oauthSuccess === 'true') {
      // OAuth succeeded
      setTimeout(() => {
        // Reload OAuth status to reflect the new connection
        loadOAuthStatus();
        checkServiceConnections();
        alert('Google account connected successfully!');
      }, 1000);

      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (oauthError === 'true') {
      // OAuth failed
      const errorMessage = error ? decodeURIComponent(error) : 'OAuth connection failed';
      alert(`Failed to connect Google account: ${errorMessage}`);

      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  };

  const loadOAuthStatus = async () => {
    try {
      const status = await apiService.getOAuthStatus();
      setOauthStatus(status);
    } catch (error) {
      console.error('Failed to load OAuth status:', error);
    }
  };

  const handleGoogleConnect = async () => {
    setIsLoading(true);
    try {
      const result = await apiService.initiateGoogleOAuth();
      // Redirect to Google OAuth
      if (result.authorization_url) {
        window.location.href = result.authorization_url;
      } else if (result.auth_url) {
        window.location.href = result.auth_url;
      } else {
        throw new Error('No authorization URL received');
      }
    } catch (error) {
      console.error('Failed to initiate OAuth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleDisconnect = async () => {
    setIsLoading(true);
    try {
      await apiService.disconnectGoogleOAuth();
      await loadOAuthStatus(); // Refresh status
    } catch (error) {
      console.error('Failed to disconnect OAuth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const generateMCPConfig = async () => {
    console.log('🔧 Generate MCP Config clicked');
    console.log('Employee data:', employee);

    if (!employee?.email) {
      console.error('❌ Employee email not available:', employee);
      alert('Employee information not available. Please refresh the page.');
      return;
    }

    if (!employee?.workspace?.slug) {
      console.error('❌ Workspace slug not available:', employee.workspace);
      alert('Workspace information not available. Please refresh the page.');
      return;
    }

    console.log(`📧 Using email: ${employee.email}`);
    console.log(`🏢 Using workspace: ${employee.workspace.slug}`);

    setMcpLoading(true);
    try {
      const requestBody = new URLSearchParams({
        employee_email: employee.email,
        workspace_slug: employee.workspace.slug,
        days_valid: '90'
      });

      console.log('📤 Request body:', requestBody.toString());

      const response = await fetch(`${process.env.NEXT_PUBLIC_AUTH_URL || 'http://localhost:8003'}/admin/claude-desktop-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: requestBody
      });

      console.log(`📥 Response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ MCP Config Error Response:', errorText);
        throw new Error(`Failed to generate MCP configuration (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ MCP Config generated successfully:', result);
      setMcpConfig(result);

      // Show success message
      alert('Claude Desktop configuration generated successfully!');
    } catch (error) {
      console.error('❌ Failed to generate MCP config:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Failed to generate MCP configuration: ${errorMessage}`);
    } finally {
      setMcpLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  const downloadConfig = () => {
    if (!mcpConfig?.config) return;

    const configJson = JSON.stringify(mcpConfig.config, null, 2);
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'claude-desktop-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const checkServiceConnections = async () => {
    try {
      const status = await apiService.getOAuthStatus();
      if (status?.connected && status?.scopes) {
        // Check which services are connected based on scopes
        const connections = {
          gmail: status.scopes.some((scope: string) => scope.includes('gmail')),
          drive: status.scopes.some((scope: string) => scope.includes('drive')),
          calendar: status.scopes.some((scope: string) => scope.includes('calendar'))
        };
        setServiceConnections(connections);
      } else {
        // Reset connections if not connected
        setServiceConnections({
          gmail: false,
          drive: false,
          calendar: false
        });
      }
    } catch (error) {
      console.error('Failed to check service connections:', error);
      // Reset connections on error
      setServiceConnections({
        gmail: false,
        drive: false,
        calendar: false
      });
    }
  };

  const connectService = async (service: string) => {
    setConnectingService(service);
    try {
      const result = await apiService.initiateGoogleOAuth([service], `${window.location.origin}/app/settings`);
      // The API returns authorization_url, not auth_url
      if (result.authorization_url) {
        window.location.href = result.authorization_url;
      } else if (result.auth_url) {
        window.location.href = result.auth_url;
      } else {
        throw new Error('No authorization URL received');
      }
    } catch (error) {
      console.error(`Failed to connect ${service}:`, error);
      alert(`Failed to connect ${service}. Please try again.`);
    } finally {
      setConnectingService(null);
    }
  };

  const disconnectService = async (service: string) => {
    try {
      // For now, disconnect all Google services
      // In a more advanced implementation, you'd disconnect individual services
      await apiService.disconnectGoogleOAuth();
      setServiceConnections({
        gmail: false,
        drive: false,
        calendar: false
      });
      setOauthStatus(null);
      alert(`${service} disconnected successfully`);
    } catch (error) {
      console.error(`Failed to disconnect ${service}:`, error);
      alert(`Failed to disconnect ${service}. Please try again.`);
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900 text-white">
        <Sidebar />
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* Top bar */}
          <div className="bg-gray-900 border-b border-gray-800 flex-shrink-0">
            <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-white">{employee?.workspace?.name || 'Workspace'}</h1>
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-green-400 text-sm">Live</span>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {employee?.name?.split(' ').map(n => n[0]).join('') || 'U'}
                </div>
                <span className="text-white text-sm">{employee?.name || 'User'}</span>
              </div>
            </div>
          </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Header */}
            <div>
              <h2 className="text-2xl font-bold text-white">Settings</h2>
              <p className="text-gray-400 mt-1">Manage your workspace preferences and configurations</p>
            </div>

            {/* Settings Sections */}
            <div className="space-y-6">
              {/* Profile Settings */}
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <User className="w-5 h-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white">Profile Settings</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                    <input
                      type="text"
                      defaultValue={employee?.name || ''}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
                    <input
                      type="email"
                      defaultValue={employee?.email || ''}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Role</label>
                    <input
                      type="text"
                      defaultValue={employee?.role || ''}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Workspace</label>
                    <input
                      type="text"
                      defaultValue={employee?.workspace?.name || ''}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* Integration Hub */}
              <div className="bg-gray-800 rounded-lg p-6">
                <IntegrationHub />
              </div>

              {/* Claude Desktop MCP Configuration */}
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <Monitor className="w-5 h-5 text-purple-400" />
                  <h3 className="text-lg font-semibold text-white">Claude Desktop Configuration</h3>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <p className="text-white font-medium">MCP Server Configuration</p>
                        <p className="text-gray-400 text-sm">Generate configuration for Claude Desktop to access your workspace tools</p>

                      </div>
                      <button
                        onClick={generateMCPConfig}
                        disabled={mcpLoading || !employee?.email || !employee?.workspace?.slug}
                        className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2"
                      >
                        {mcpLoading ? (
                          <>
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            <span>Generating...</span>
                          </>
                        ) : (
                          <>
                            <RefreshCw className="w-4 h-4" />
                            <span>Generate Config</span>
                          </>
                        )}
                      </button>
                    </div>

                    {mcpConfig && (
                      <div className="space-y-4">
                        <div className="bg-gray-800 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <p className="text-sm font-medium text-gray-300">Configuration JSON</p>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => copyToClipboard(JSON.stringify(mcpConfig.config, null, 2))}
                                className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 rounded text-xs flex items-center space-x-1"
                              >
                                <Copy className="w-3 h-3" />
                                <span>Copy</span>
                              </button>
                              <button
                                onClick={downloadConfig}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs flex items-center space-x-1"
                              >
                                <Download className="w-3 h-3" />
                                <span>Download Config</span>
                              </button>
                              <button
                                onClick={() => {
                                  const serverUrl = mcpConfig?.serverDownloadUrl || `${window.location.origin}/api/download/uru-claude-server.js`;
                                  window.open(serverUrl, '_blank');
                                }}
                                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs flex items-center space-x-1"
                              >
                                <Download className="w-3 h-3" />
                                <span>Download Server</span>
                              </button>
                            </div>
                          </div>
                          <pre className="bg-gray-900 rounded p-3 text-xs text-gray-300 overflow-x-auto">
                            {JSON.stringify(mcpConfig.config, null, 2)}
                          </pre>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-800 rounded-lg p-3">
                            <p className="text-xs font-medium text-gray-300 mb-1">Employee Token</p>
                            <div className="flex items-center space-x-2">
                              <code className="flex-1 bg-gray-900 rounded px-2 py-1 text-xs text-gray-300 font-mono truncate">
                                {mcpConfig.claude_desktop_token?.substring(0, 20)}...
                              </code>
                              <button
                                onClick={() => copyToClipboard(mcpConfig.claude_desktop_token)}
                                className="bg-gray-600 hover:bg-gray-500 text-white px-2 py-1 rounded text-xs"
                              >
                                <Copy className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                          <div className="bg-gray-800 rounded-lg p-3">
                            <p className="text-xs font-medium text-gray-300 mb-1">Expires</p>
                            <p className="text-xs text-gray-400">
                              {mcpConfig.expires_at ? new Date(mcpConfig.expires_at).toLocaleDateString() : 'N/A'}
                            </p>
                          </div>
                        </div>

                        <div className="bg-blue-600/10 border border-blue-600/30 rounded-lg p-4">
                          <h4 className="text-blue-400 font-medium text-sm mb-2">Setup Instructions</h4>
                          {mcpConfig?.setupInstructions ? (
                            <ol className="text-xs text-gray-300 space-y-1">
                              {mcpConfig.setupInstructions.map((instruction, index) => (
                                <li key={index}>{instruction}</li>
                              ))}
                            </ol>
                          ) : (
                            <ol className="text-xs text-gray-300 space-y-1">
                              <li>1. Copy the configuration JSON above</li>
                              <li>2. Open Claude Desktop settings</li>
                              <li>3. Add the configuration to your MCP servers</li>
                              <li>4. Restart Claude Desktop</li>
                              <li>5. You'll now have access to your workspace tools in Claude</li>
                            </ol>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Notification Settings */}
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <Bell className="w-5 h-5 text-yellow-400" />
                  <h3 className="text-lg font-semibold text-white">Notification Settings</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Email Notifications</p>
                      <p className="text-gray-400 text-sm">Receive email alerts for important insights</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Real-time Alerts</p>
                      <p className="text-gray-400 text-sm">Get notified of urgent patterns or anomalies</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Weekly Reports</p>
                      <p className="text-gray-400 text-sm">Receive weekly intelligence summaries</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Data & Privacy */}
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <Shield className="w-5 h-5 text-green-400" />
                  <h3 className="text-lg font-semibold text-white">Data & Privacy</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Data Retention</p>
                      <p className="text-gray-400 text-sm">How long to keep analyzed data</p>
                    </div>
                    <select className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option>12 months</option>
                      <option>24 months</option>
                      <option>36 months</option>
                      <option>Indefinite</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Anonymous Analytics</p>
                      <p className="text-gray-400 text-sm">Help improve the platform with usage data</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Integration Settings */}
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <Zap className="w-5 h-5 text-purple-400" />
                  <h3 className="text-lg font-semibold text-white">Integration Settings</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Auto-sync Frequency</p>
                      <p className="text-gray-400 text-sm">How often to sync data sources</p>
                    </div>
                    <select className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option>Every 5 minutes</option>
                      <option>Every 15 minutes</option>
                      <option>Every hour</option>
                      <option>Manual only</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">API Rate Limiting</p>
                      <p className="text-gray-400 text-sm">Limit API calls to prevent overuse</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between">
                <button
                  onClick={handleLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Sign Out
                </button>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
    </ProtectedRoute>
  );
}

// Prevent static generation for protected routes
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
