import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { useAuth } from '../components/auth/AuthContext';
import { Eye, EyeOff, AlertCircle } from 'lucide-react';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [workspaceSlug, setWorkspaceSlug] = useState('ignition-consultants');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { login, isAuthenticated } = useAuth();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/app').catch((error) => {
        // Handle navigation error silently
      });
    }
  }, [isAuthenticated, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login(email, password, workspaceSlug);

      // Small delay to ensure state is updated
      setTimeout(() => {
        router.push('/app').catch((error) => {
          // Handle navigation error silently
        });
      }, 100);
    } catch (err: any) {
      // Provide user-friendly error messages
      let errorMessage = 'Login failed. Please try again.';

      if (err.message) {
        if (err.message.includes('Network connection failed')) {
          errorMessage = 'Unable to connect to the server. Please check your internet connection and try again.';
        } else if (err.message.includes('authentication_unavailable') || err.message.includes('AUTH_SERVICE_DISABLED')) {
          errorMessage = 'Authentication service is currently unavailable. The database connection may be down. Please contact support.';
        } else if (err.message.includes('Invalid credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (err.message.includes('Account not activated')) {
          errorMessage = 'Your account is not yet activated. Please check your email for activation instructions.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-8">
            <UruLogo size="2xl" />
          </div>
          <h2 className="text-4xl font-black text-white tracking-tight mb-4">
            Sign in to your workspace
          </h2>
          <p className="text-xl text-gray-300 font-light">
            Access your business intelligence platform
          </p>
        </div>

        {/* Login Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-600/10 border border-red-600/30 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-2">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                <span className="text-red-400 text-sm">{error}</span>
              </div>
              {(error.includes('unavailable') || error.includes('connection') || error.includes('server')) && (
                <div className="mt-2 ml-8">
                  <a
                    href="/diagnose"
                    className="text-red-300 underline hover:text-red-200 text-xs"
                  >
                    Run system diagnostics to troubleshoot this issue →
                  </a>
                </div>
              )}
            </div>
          )}

          <div className="space-y-4">
            {/* Workspace */}
            <div>
              <label htmlFor="workspace" className="block text-sm font-medium text-gray-300 mb-2">
                Workspace
              </label>
              <input
                id="workspace"
                name="workspace"
                type="text"
                value={workspaceSlug}
                onChange={(e) => setWorkspaceSlug(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="your-workspace"
                required
              />
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
                required
              />
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 pr-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="••••••••"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Signing in...</span>
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-600/10 border border-red-600/30 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0"></div>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Help Text */}
          <div className="text-center space-y-3">
            <p className="text-gray-400 text-sm">
              Need access? Contact your workspace administrator
            </p>
            <div className="border-t border-gray-700 pt-3">
              <p className="text-gray-400 text-sm mb-2">
                Have an invitation link?
              </p>
              <a
                href="/request-access"
                className="text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium"
              >
                Request Access →
              </a>
            </div>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center">
          <p className="text-gray-500 text-xs">
            Powered by Uru Workspace Platform
          </p>
        </div>
      </div>
    </div>
  );
}

// Prevent static generation for this page since it uses useRouter
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
